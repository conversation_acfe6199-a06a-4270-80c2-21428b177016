import discord
import asyncio
import random
import numpy as np
import time
from datetime import datetime, timezone

from PIL import Image, ImageDraw, ImageFont
from discord.ext import commands
from discord import app_commands
from io import BytesIO

from utilities import checks
from utilities import converters
from utilities import decorators
from utilities import pagination


async def setup(bot):
    await bot.add_cog(Misc(bot))


class AFKView(discord.ui.View):
    """View for AFK DM preference selection"""

    def __init__(self, user, reason, cog):
        super().__init__(timeout=60.0)
        self.user = user
        self.reason = reason
        self.cog = cog
        self.responded = False

    @discord.ui.button(label="DM on mention", style=discord.ButtonStyle.green)
    async def dm_on_mention(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user.id:
            return await interaction.response.send_message("This is not your AFK session!", ephemeral=True)

        if self.responded:
            return await interaction.response.send_message("You already selected an option!", ephemeral=True)

        self.responded = True

        # Set user as AFK with DM preference
        afk_time = datetime.now(timezone.utc)
        self.cog.afk_users[self.user.id] = {
            'reason': self.reason,
            'time': afk_time,
            'dm_on_mention': True,
            'mentions': []
        }

        await self.cog.save_afk_user(self.user.id, self.reason, True)

        # Update the embed
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> @{self.user.display_name}: You're now AFK with the status: {self.reason}\n*You will receive DMs when mentioned*",
            color=0x323339
        )

        # Disable all buttons
        for item in self.children:
            item.disabled = True

        await interaction.response.edit_message(embed=embed, view=self)

    @discord.ui.button(label="No DMs", style=discord.ButtonStyle.red)
    async def no_dms(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user.id:
            return await interaction.response.send_message("This is not your AFK session!", ephemeral=True)

        if self.responded:
            return await interaction.response.send_message("You already selected an option!", ephemeral=True)

        self.responded = True

        # Set user as AFK without DM preference
        afk_time = datetime.now(timezone.utc)
        self.cog.afk_users[self.user.id] = {
            'reason': self.reason,
            'time': afk_time,
            'dm_on_mention': False,
            'mentions': []
        }

        await self.cog.save_afk_user(self.user.id, self.reason, False)

        # Update the embed
        embed = discord.Embed(
            description=f"<:pass:1395855305270755399> @{self.user.display_name}: You're now AFK with the status: {self.reason}\n*You will not receive DMs when mentioned*",
            color=0x323339
        )

        # Disable all buttons
        for item in self.children:
            item.disabled = True

        await interaction.response.edit_message(embed=embed, view=self)

    async def on_timeout(self):
        # Default to no DMs if user doesn't respond
        if not self.responded:
            afk_time = datetime.now(timezone.utc)
            self.cog.afk_users[self.user.id] = {
                'reason': self.reason,
                'time': afk_time,
                'dm_on_mention': False,
                'mentions': []
            }
            await self.cog.save_afk_user(self.user.id, self.reason, False)


class Misc(commands.Cog):
    """
    Miscellaneous stuff.
    """

    def __init__(self, bot):
        self.bot = bot
        # AFK storage: {user_id: {'reason': str, 'time': timestamp, 'dm_on_mention': bool, 'mentions': []}}
        self.afk_users = {}
        # Load AFK data from database on startup
        self.bot.loop.create_task(self.load_afk_data())

    async def load_afk_data(self):
        """Load AFK data from database on startup"""
        await self.bot.wait_until_ready()
        if not self.bot.cxn:
            return

        try:
            # Create AFK table if it doesn't exist
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS afk_users (
                    user_id BIGINT PRIMARY KEY,
                    reason TEXT,
                    afk_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
                    dm_on_mention BOOLEAN DEFAULT FALSE
                );
            """)

            # Create AFK mentions table if it doesn't exist
            await self.bot.cxn.execute("""
                CREATE TABLE IF NOT EXISTS afk_mentions (
                    id BIGSERIAL PRIMARY KEY,
                    user_id BIGINT,
                    mentioner_id BIGINT,
                    mentioner_name TEXT,
                    channel_id BIGINT,
                    guild_id BIGINT,
                    mention_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
                    FOREIGN KEY (user_id) REFERENCES afk_users(user_id) ON DELETE CASCADE
                );
            """)

            # Load existing AFK users
            rows = await self.bot.cxn.fetch("SELECT * FROM afk_users")
            for row in rows:
                self.afk_users[row['user_id']] = {
                    'reason': row['reason'],
                    'time': row['afk_time'],
                    'dm_on_mention': row['dm_on_mention'],
                    'mentions': []
                }

                # Load mentions for this user
                mentions = await self.bot.cxn.fetch(
                    "SELECT * FROM afk_mentions WHERE user_id = $1 ORDER BY mention_time DESC",
                    row['user_id']
                )
                self.afk_users[row['user_id']]['mentions'] = [
                    {
                        'mentioner_id': m['mentioner_id'],
                        'mentioner_name': m['mentioner_name'],
                        'channel_id': m['channel_id'],
                        'guild_id': m['guild_id'],
                        'time': m['mention_time']
                    }
                    for m in mentions
                ]
        except Exception as e:
            print(f"Error loading AFK data: {e}")

    async def save_afk_user(self, user_id, reason, dm_on_mention):
        """Save AFK user to database"""
        if not self.bot.cxn:
            return

        try:
            await self.bot.cxn.execute("""
                INSERT INTO afk_users (user_id, reason, dm_on_mention)
                VALUES ($1, $2, $3)
                ON CONFLICT (user_id)
                DO UPDATE SET reason = $2, afk_time = NOW() AT TIME ZONE 'UTC', dm_on_mention = $3
            """, user_id, reason, dm_on_mention)
        except Exception as e:
            print(f"Error saving AFK user: {e}")

    async def remove_afk_user(self, user_id):
        """Remove AFK user from database"""
        if not self.bot.cxn:
            return

        try:
            await self.bot.cxn.execute("DELETE FROM afk_users WHERE user_id = $1", user_id)
            await self.bot.cxn.execute("DELETE FROM afk_mentions WHERE user_id = $1", user_id)
        except Exception as e:
            print(f"Error removing AFK user: {e}")

    async def add_afk_mention(self, user_id, mentioner_id, mentioner_name, channel_id, guild_id):
        """Add a mention to the database"""
        if not self.bot.cxn:
            return

        try:
            await self.bot.cxn.execute("""
                INSERT INTO afk_mentions (user_id, mentioner_id, mentioner_name, channel_id, guild_id)
                VALUES ($1, $2, $3, $4, $5)
            """, user_id, mentioner_id, mentioner_name, channel_id, guild_id)
        except Exception as e:
            print(f"Error adding AFK mention: {e}")

    def format_time_ago(self, timestamp):
        """Format timestamp to relative time"""
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

        now = datetime.now(timezone.utc)
        if timestamp.tzinfo is None:
            timestamp = timestamp.replace(tzinfo=timezone.utc)

        diff = now - timestamp

        if diff.days > 0:
            if diff.days == 1:
                return "1 day ago"
            return f"{diff.days} days ago"

        hours = diff.seconds // 3600
        if hours > 0:
            if hours == 1:
                return "1 hour ago"
            return f"{hours} hours ago"

        minutes = diff.seconds // 60
        if minutes > 0:
            if minutes == 1:
                return "1 minute ago"
            return f"{minutes} minutes ago"

        seconds = diff.seconds
        if seconds == 1:
            return "1 second ago"
        return f"{seconds} seconds ago"

    @decorators.command(
        brief="Set yourself as AFK with an optional reason",
        aliases=["away"]
    )
    @checks.cooldown()
    async def afk(self, ctx, *, reason: str = "AFK"):
        """
        Usage: {0}afk [reason]
        Alias: {0}away
        Output: Sets you as AFK with an optional reason
        Notes: You'll be automatically removed from AFK when you send a message
        """
        user_id = ctx.author.id

        # Check if user is already AFK
        if user_id in self.afk_users:
            return await ctx.fail("You are already AFK!")

        # Create view with DM preference buttons
        view = AFKView(ctx.author, reason, self)
        embed = discord.Embed(
            description = f"<:pass:1395855305270755399> {ctx.author.mention}: You're now AFK with the status: {reason}",
            color=0x323339
        )

        await ctx.send_or_reply(embed=embed, view=view)

    @decorators.command(
        brief="View your AFK mentions",
        aliases=["afkmentions", "mentions"]
    )
    @checks.cooldown()
    async def afk_mentions(self, ctx):
        """
        Usage: {0}afk_mentions
        Aliases: {0}afkmentions, {0}mentions
        Output: Shows all mentions you received while AFK
        """
        user_id = ctx.author.id

        if user_id not in self.afk_users:
            return await ctx.fail("You are not currently AFK!")

        mentions = self.afk_users[user_id]['mentions']

        if not mentions:
            return await ctx.send_or_reply("You have no mentions while AFK.")

        # Create embed with mentions
        embed = discord.Embed(
            title=f"{ctx.author.mention}",
            description="**Last Mentions**",
            color=0x323339
        )

        # Show up to 10 most recent mentions
        for i, mention in enumerate(mentions[:10]):
            channel = self.bot.get_channel(mention['channel_id'])
            channel_name = f"# {channel.name}" if channel else "Unknown Channel"
            time_ago = self.format_time_ago(mention['time'])

            embed.add_field(
                name=f"{i+1}. {mention['mentioner_name']} pinged",
                value=f"{time_ago} ({channel_name})",
                inline=False
            )

        total_mentions = len(mentions)
        embed.set_footer(text=f"Page 1/1 ({total_mentions} entr{'y' if total_mentions == 1 else 'ies'})")

        await ctx.send_or_reply(embed=embed)

    @decorators.command(
        brief="Remove your AFK status manually",
        aliases=["unafk", "back"]
    )
    @checks.cooldown()
    async def remove_afk(self, ctx):
        """
        Usage: {0}remove_afk
        Aliases: {0}unafk, {0}back
        Output: Manually removes your AFK status
        """
        user_id = ctx.author.id

        if user_id not in self.afk_users:
            return await ctx.fail("You are not currently AFK!")

        afk_data = self.afk_users[user_id]
        time_away = self.format_time_ago(afk_data['time'])

        # Remove from AFK
        del self.afk_users[user_id]
        await self.remove_afk_user(user_id)

        # Send welcome back message
        embed = discord.Embed(
            description=f"<:wave:1396802560613941319> {ctx.author.mention}: Welcome back, you were away for {time_away}",
            color=0x323339
        )

        await ctx.send_or_reply(embed=embed)

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle AFK functionality and bot mentions"""
        if not message.guild or message.author.bot:
            return

        # Check if bot is mentioned
        if self.bot.user in message.mentions:
            # Get the server prefixes
            prefixes = self.bot.get_raw_guild_prefixes(message.guild.id)

            # Get the first prefix or default
            if prefixes:
                prefix = prefixes[0]
            else:
                prefix = self.bot.mode.DEFAULT_PREFIX

            embed = discord.Embed(
                description=f"Hey! My prefix is `{prefix}`",
                color=0x323339
            )
            embed.set_footer(text=f"Try {prefix}help for a list of commands")

            try:
                await message.channel.send(embed=embed)
            except:
                pass  # Ignore if we can't send messages

        author_id = message.author.id

        # Check if the message author is AFK and remove them
        if author_id in self.afk_users:
            afk_data = self.afk_users[author_id]
            time_away = self.format_time_ago(afk_data['time'])

            # Remove from AFK
            del self.afk_users[author_id]
            await self.remove_afk_user(author_id)

            # Send welcome back message
            embed = discord.Embed(
                description=f"<:wave:1396802560613941319> @{message.author.display_name}: Welcome back, you were away for {time_away}",
                color=0x323339
            )

            try:
                await message.channel.send(embed=embed, delete_after=10)
            except:
                pass

        # Check for mentions of AFK users
        if message.mentions:
            for mentioned_user in message.mentions:
                if mentioned_user.id in self.afk_users and mentioned_user.id != author_id:
                    afk_data = self.afk_users[mentioned_user.id]
                    time_ago = self.format_time_ago(afk_data['time'])

                    # Add mention to the AFK user's mention list
                    mention_data = {
                        'mentioner_id': author_id,
                        'mentioner_name': message.author.display_name,
                        'channel_id': message.channel.id,
                        'guild_id': message.guild.id,
                        'time': datetime.now(timezone.utc)
                    }

                    self.afk_users[mentioned_user.id]['mentions'].append(mention_data)
                    await self.add_afk_mention(
                        mentioned_user.id, author_id, message.author.display_name,
                        message.channel.id, message.guild.id
                    )

                    # Send AFK status message
                    embed = discord.Embed(
                        description=f"<a:zzz:1396804712661123112> @{mentioned_user.display_name} is AFK: {afk_data['reason']} - {time_ago}",
                        color=0x323339
                    )

                    try:
                        await message.channel.send(embed=embed, delete_after=15)
                    except:
                        pass

                    # Send DM if user opted in
                    if afk_data['dm_on_mention']:
                        try:
                            dm_embed = discord.Embed(
                                title="AFK Mention",
                                description=f"You were mentioned by **{message.author.display_name}** in **{message.guild.name}** #{message.channel.name}",
                                color=0x323339
                            )
                            dm_embed.add_field(
                                name="Message",
                                value=message.content[:1000] + ("..." if len(message.content) > 1000 else ""),
                                inline=False
                            )
                            dm_embed.add_field(
                                name="Jump to Message",
                                value=f"[Click here]({message.jump_url})",
                                inline=False
                            )

                            await mentioned_user.send(embed=dm_embed)
                        except:
                            pass  # User has DMs disabled

    @decorators.command(
        brief="Finds the 'best' definition of a word", aliases=["urban"]
    )
    @checks.cooldown()
    async def define(self, ctx, *, search: commands.clean_content):
        """
        Usage: {0}define <search>
        Alias: {0}urban
        Output:
            Attempts to fetch an urban dictionary
            definition based off of your search query.
        """
        async with ctx.channel.typing():
            try:
                url = await self.bot.http_utils.get(
                    f"https://api.urbandictionary.com/v0/define?term={search}",
                    res_method="json",
                )
            except Exception:
                return await ctx.send("Urban API returned invalid data... fuck them.")

            if not url:
                return await ctx.send("I think the API broke...")

            if not len(url["list"]):
                return await ctx.send("Couldn't find your search in the dictionary...")

            result = sorted(
                url["list"], reverse=True, key=lambda g: int(g["thumbs_up"])
            )[0]

            definition = result["definition"]
            if len(definition) >= 2000:
                definition = definition[:2000]
                definition = definition.rsplit(" ", 1)[0]
                definition += "..."

            await ctx.send_or_reply(
                f"📚 Definitions for **{result['word']}**```yaml\n{definition}```"
            )

    def ascii_image(self, path):
        image = Image.open(path)
        sc = 0.2
        gcf = 0.2
        bgcolor = "#060e16"
        re_list = list(" .,:;irsXA253hMHGS#9B&@")
        chars = np.asarray(re_list)
        font = ImageFont.load_default()
        font = ImageFont.truetype("./data/assets/Monospace.ttf", 10)
        letter_width = font.getbbox("x")[2]
        letter_height = font.getbbox("x")[3]
        wcf = letter_height / letter_width
        img = image.convert("RGBA")

        width_by_letter = round(img.size[0] * sc * wcf)
        height_by_letter = round(img.size[1] * sc)
        s = (width_by_letter, height_by_letter)
        img = img.resize(s)
        img = np.sum(np.asarray(img), axis=2)
        img -= img.min()
        img = (1.0 - img / img.max()) ** gcf * (chars.size - 1)
        lines = ("".join(r) for r in chars[len(chars) - img.astype(int) - 1])
        new_img_width = letter_width * width_by_letter
        new_img_height = letter_height * height_by_letter
        new_img = Image.new("RGBA", (new_img_width, new_img_height), bgcolor)
        draw = ImageDraw.Draw(new_img)
        y = 0
        for line in lines:
            draw.text((0, y), line, "#FFFFFF", font=font)
            y += letter_height

        buffer = BytesIO()
        new_img.save(buffer, "png")  # 'save' function for PIL
        buffer.seek(0)
        return buffer

    @decorators.command(
        name="matrix",
        aliases=["ascii", "print"],
        brief="Generate a dot matrix of an image.",
    )
    @checks.cooldown()
    async def matrix(self, ctx, *, url=None):
        """
        Usage: {0}print [user, image url, or image attachment]
        Aliases: {0}matrix, {0}ascii
        Output: Creates a dot matrix of the passed image.
        Notes: Accepts a url or picks the first attachment.
        """

        if url == None and len(ctx.message.attachments) == 0:
            await ctx.send_or_reply(
                "Usage: `{}matrix [user, url, or attachment]`".format(ctx.prefix)
            )
            return

        if url == None:
            url = ctx.message.attachments[0].url

        # Let's check if the "url" is actually a user
        try:
            test_user = await converters.DiscordUser().convert(ctx, url)
            url = test_user.display_avatar.url
        except Exception:
            pass

        message = await ctx.load("Generating dot matrix...")

        try:
            image_bytes = await self.bot.http_utils.get(url, res_method="read")
        except Exception:
            await message.edit(content="Invalid url or attachment.")
            return

        path = BytesIO(image_bytes)
        if not path:
            await message.edit(content="Invalid url or attachment.")
            return

        image = self.ascii_image(path)
        await ctx.rep_or_ref(file=discord.File(image, filename="matrix.png"))
        await message.delete()

    @decorators.command(brief="Just try it and see.")
    @checks.cooldown()
    async def size(self, ctx, *, user: converters.DiscordUser = None):
        user = user or ctx.author

        def find_size(snowflake):
            s = 0
            while snowflake:
                snowflake //= 10
                s += snowflake % 10
                return (s % 10) * 2

        size = find_size(user.id)
        if user.id == self.bot.developer_id:
            size *= 5

        await ctx.send_or_reply(f"**{user.display_name}'s** size: 8{'=' * size}D")

